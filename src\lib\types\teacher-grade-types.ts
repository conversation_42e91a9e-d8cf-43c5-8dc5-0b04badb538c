// Teacher class and subject types
export interface TeacherClass {
  id: string
  name: string
  grade_level: number
}

export interface TeacherSubject {
  id: string
  name_vietnamese: string
  code: string
  regular_grade_count: number
}

// Database response types
export interface TeacherClassAssignment {
  class: TeacherClass
}

export interface TeacherSubjectAssignment {
  subject: {
    id: string
    name_vietnamese: string
    code: string
  }
}

// Vietnamese education grade count standards
export const VIETNAMESE_GRADE_STANDARDS: Record<string, number> = {
  'Ngữ văn': 4,
  'Toán': 4,
  '<PERSON><PERSON><PERSON><PERSON> ngữ 1': 4,
  '<PERSON><PERSON><PERSON> sử': 3,
  '<PERSON><PERSON><PERSON><PERSON> dục thể chất': 2,
  '<PERSON><PERSON><PERSON><PERSON> dục quốc phòng và an ninh': 2,
  'Địa lí': 3,
  '<PERSON><PERSON><PERSON><PERSON> dục kinh tế và pháp luật': 3,
  'Vật lí': 3,
  '<PERSON><PERSON><PERSON> học': 3,
  '<PERSON><PERSON> học': 3,
  '<PERSON><PERSON><PERSON> nghệ': 3,
  '<PERSON> học': 3,
  '<PERSON><PERSON> nhạc': 2,
  '<PERSON><PERSON> thuật': 2,
  '<PERSON><PERSON><PERSON> động trải nghiệm, h<PERSON>ớng nghiệp': 2,
  '<PERSON><PERSON><PERSON> dung giáo dục của địa phương': 2,
  'Tiếng dân tộc thiểu số': 4,
  'Ngoại ngữ 2': 4
}
