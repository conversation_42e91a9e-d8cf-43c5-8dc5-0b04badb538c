import { CheckCircle, XCircle, Clock } from "lucide-react"

// Shared status configuration for various components
export const statusConfig = {
  pending: {
    label: "Đang chờ",
    variant: "secondary" as const,
    icon: Clock,
    color: "text-yellow-600"
  },
  approved: {
    label: "<PERSON><PERSON> chấp thuận",
    variant: "default" as const,
    icon: CheckCircle,
    color: "text-green-600"
  },
  rejected: {
    label: "Đã từ chối",
    variant: "destructive" as const,
    icon: XCircle,
    color: "text-red-600"
  }
} as const

export type StatusType = keyof typeof statusConfig

// Day names mapping for consistent display
export const dayNames: Record<number, string> = {
  1: "<PERSON><PERSON><PERSON>",
  2: "<PERSON><PERSON><PERSON>",
  3: "<PERSON><PERSON><PERSON>",
  4: "<PERSON><PERSON><PERSON>",
  5: "<PERSON><PERSON><PERSON>",
  6: "<PERSON><PERSON><PERSON>",
  7: "<PERSON><PERSON>"
}

// Helper function to get status configuration
export function getStatusConfig(status: string) {
  return statusConfig[status as StatusType] || statusConfig.pending
}
