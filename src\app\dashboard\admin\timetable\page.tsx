﻿import { Metadata } from "next";
import { CalendarProvider } from "@/features/timetable/components/event-calendar/calendar-context";
import TimetableBigCalendar from "@/features/timetable/components/timetable-big-calendar";

export const metadata: Metadata = {
  title: "<PERSON>uản lý thời khóa biểu",
  description: "<PERSON>uản lý lịch học, gi<PERSON>o viên và thời khóa biểu theo tuần",
};

export default function TimetablePage() {
  return (
    <div className="p-6">
      <CalendarProvider>
        <div className="flex flex-1 flex-col gap-4">
          <TimetableBigCalendar />
        </div>
      </CalendarProvider>
    </div>
  );
}

