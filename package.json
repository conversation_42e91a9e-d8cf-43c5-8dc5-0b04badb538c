{"name": "src", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/is-prop-valid": "^1.3.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@google/genai": "^1.14.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@remixicon/react": "^4.6.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.5", "@types/nodemailer": "^6.4.18", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "framer-motion": "^12.23.12", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.540.0", "next": "15.4.2", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.12", "@types/jspdf": "^2.0.0", "@types/node": "^20.19.11", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "eslint": "^9.33.0", "eslint-config-next": "15.4.2", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "webpack-bundle-analyzer": "^4.10.2"}, "optionalDependencies": {"@tailwindcss/oxide-linux-x64-gnu": "^4.1.12", "lightningcss-linux-x64-gnu": "^1.30.1"}}