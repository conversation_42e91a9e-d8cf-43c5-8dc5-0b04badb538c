﻿import { SharedNotificationsPage, type NotificationPageConfig } from '@/features/notifications/components/notifications/shared-notifications-page'

const adminConfig: NotificationPageConfig = {
  role: 'admin',
  title: 'Thông báo',
  description: '<PERSON><PERSON><PERSON> và quản lý thông báo tới giáo viên, học sinh và phụ huynh',
  emptyStateMessage: 'G<PERSON><PERSON> thông báo đầu tiên để bắt đầu',
  dashboardPath: '/dashboard/admin',
  canSendNotifications: true,
  useSidebarLayout: false
}

export default function AdminNotificationsPage() {
  return <SharedNotificationsPage config={adminConfig} />
}
