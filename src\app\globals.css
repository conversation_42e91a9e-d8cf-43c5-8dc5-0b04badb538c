/* stylelint-disable-next-line at-rule-no-unknown */
@import "tailwindcss";

/* stylelint-disable-next-line at-rule-no-unknown */
@custom-variant dark (&:is(.dark *));

/* stylelint-disable-next-line at-rule-no-unknown */
@theme inline {
  /* Color System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  /* Orange Brand Palette - Modern Minimalist */
  --color-orange-50: oklch(0.98 0.02 48);
  --color-orange-100: oklch(0.95 0.05 48);
  --color-orange-200: oklch(0.90 0.10 48);
  --color-orange-300: oklch(0.82 0.15 48);
  --color-orange-400: oklch(0.75 0.19 48);
  --color-orange-500: oklch(0.68 0.22 48);
  --color-orange-600: oklch(0.60 0.20 48);
  --color-orange-700: oklch(0.52 0.18 48);
  --color-orange-800: oklch(0.44 0.15 48);
  --color-orange-900: oklch(0.36 0.12 48);
  --color-orange-950: oklch(0.28 0.10 48);

  /* Typography */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Border Radius - Modern Minimalist (larger radii) */
  --radius-xs: 0.375rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.25rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;

  /* Spacing - Progressive for responsive */
  --spacing-mobile: 0.5rem;
  --spacing-tablet: 1rem;
  --spacing-desktop: 1.5rem;
  --spacing-wide: 2rem;

  /* Container Max Width */
  --container-max: 100rem;

  /* Shadows - Light and modern */
  --shadow-soft: 0 2px 8px oklch(0 0 0 / 0.04);
  --shadow-medium: 0 4px 16px oklch(0 0 0 / 0.08);
  --shadow-strong: 0 8px 32px oklch(0 0 0 / 0.12);

  /* Sidebar Colors */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Chart Colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Custom Breakpoints for Ultra-wide support */
  --breakpoint-3xl: 120rem;
  --breakpoint-4xl: 160rem;
}

:root {
  /* Modern Minimalist Border Radius - larger for clean look */
  --radius: 1.5rem; /* rounded-2xl default for modern minimalist */

  /* Base Colors - Clean and spacious */
  --background: oklch(1 0 0); /* Pure white */
  --foreground: oklch(0.145 0 0); /* Deep charcoal */

  /* Card System - Elevated with soft shadows */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* Orange Brand System - Enhanced for Modern Minimalist */
  --primary: oklch(0.68 0.22 48); /* Main orange #FF6B2C equivalent */
  --primary-foreground: oklch(0.99 0.02 48); /* Near white with orange tint */

  /* Secondary - Warm grays for minimalist aesthetic */
  --secondary: oklch(0.98 0.005 48); /* Very light warm gray */
  --secondary-foreground: oklch(0.25 0.01 48); /* Dark warm gray */

  /* Muted - Spacious and clean */
  --muted: oklch(0.97 0.01 48); /* Light warm background */
  --muted-foreground: oklch(0.55 0.02 48); /* Medium warm gray */

  /* Accent - Subtle orange tint */
  --accent: oklch(0.96 0.03 48); /* Very light orange */
  --accent-foreground: oklch(0.25 0.01 48); /* Dark warm text */

  /* System Colors */
  --destructive: oklch(0.67 0.22 30); /* Red for errors */
  --border: oklch(0.94 0.01 48); /* Light warm border */
  --input: oklch(0.96 0.01 48); /* Input background */
  --ring: oklch(0.68 0.22 48); /* Focus ring matches primary */

  /* Chart Colors - Orange-centric palette */
  --chart-1: oklch(0.68 0.22 48); /* Primary orange */
  --chart-2: oklch(0.75 0.19 48); /* Lighter orange */
  --chart-3: oklch(0.60 0.20 48); /* Darker orange */
  --chart-4: oklch(0.55 0.02 48); /* Warm gray */
  --chart-5: oklch(0.82 0.15 48); /* Very light orange */

  /* Sidebar - Clean and minimal */
  --sidebar: oklch(0.99 0.005 48); /* Almost white with warm tint */
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: oklch(0.96 0.03 48); /* Light orange accent */
  --sidebar-accent-foreground: oklch(0.25 0.01 48);
  --sidebar-border: oklch(0.94 0.01 48); /* Subtle warm border */
  --sidebar-ring: oklch(0.68 0.22 48); /* Orange focus ring */
}

.dark {
  /* Dark Mode - Modern Minimalist with Orange Accent */
  --background: oklch(0.12 0.005 48); /* Very dark warm background */
  --foreground: oklch(0.98 0.01 48); /* Near white with warm tint */

  /* Cards - Elevated dark surfaces */
  --card: oklch(0.16 0.01 48); /* Dark warm card background */
  --card-foreground: oklch(0.98 0.01 48);
  --popover: oklch(0.16 0.01 48);
  --popover-foreground: oklch(0.98 0.01 48);

  /* Orange Brand - Adjusted for dark mode */
  --primary: oklch(0.72 0.20 48); /* Slightly lighter orange for contrast */
  --primary-foreground: oklch(0.12 0.005 48); /* Dark background for text */

  /* Secondary - Dark warm grays */
  --secondary: oklch(0.20 0.01 48); /* Dark warm surface */
  --secondary-foreground: oklch(0.98 0.01 48); /* Light warm text */

  /* Muted - Dark minimalist */
  --muted: oklch(0.18 0.01 48); /* Muted dark surface */
  --muted-foreground: oklch(0.70 0.02 48); /* Medium light warm text */

  /* Accent - Dark orange accent */
  --accent: oklch(0.22 0.02 48); /* Dark orange accent */
  --accent-foreground: oklch(0.98 0.01 48); /* Light text */

  /* System Colors */
  --destructive: oklch(0.70 0.22 30); /* Slightly lighter red for dark mode */
  --border: oklch(0.98 0.01 48 / 12%); /* Subtle warm border */
  --input: oklch(0.98 0.01 48 / 8%); /* Input background */
  --ring: oklch(0.72 0.20 48); /* Orange focus ring */

  /* Chart Colors - Dark mode orange palette */
  --chart-1: oklch(0.72 0.20 48); /* Primary orange */
  --chart-2: oklch(0.78 0.18 48); /* Lighter orange */
  --chart-3: oklch(0.65 0.22 48); /* Darker orange */
  --chart-4: oklch(0.60 0.03 48); /* Warm gray */
  --chart-5: oklch(0.85 0.15 48); /* Very light orange */

  /* Sidebar - Dark minimalist */
  --sidebar: oklch(0.14 0.005 48); /* Dark warm sidebar */
  --sidebar-foreground: oklch(0.98 0.01 48);
  --sidebar-primary: oklch(0.72 0.20 48); /* Orange primary */
  --sidebar-primary-foreground: oklch(0.12 0.005 48);
  --sidebar-accent: oklch(0.22 0.02 48); /* Dark orange accent */
  --sidebar-accent-foreground: oklch(0.98 0.01 48);
  --sidebar-border: oklch(0.98 0.01 48 / 10%); /* Subtle border */
  --sidebar-ring: oklch(0.72 0.20 48); /* Orange focus ring */
}

@layer base {
  * {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply border-border outline-ring/50;
  }

  body {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply bg-background text-foreground antialiased;
    /* Modern typography for readability */
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Modern Minimalist Container System */
  .container-modern {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply mx-auto px-4 sm:px-6 lg:px-8;
    max-width: var(--container-max);
  }

  /* Progressive Spacing System */
  .spacing-mobile {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply p-2;
  }

  .spacing-tablet {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply p-4 md:p-6;
  }

  .spacing-desktop {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply p-6 lg:p-8 xl:p-10;
  }

  /* Modern Card Shadows */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }

  /* Orange Brand Utilities */
  .text-orange-brand {
    color: var(--primary);
  }

  .bg-orange-brand {
    background-color: var(--primary);
  }

  .border-orange-brand {
    border-color: var(--primary);
  }
}

@layer components {
  /* Modern Minimalist Button Base */
  .btn-modern {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply inline-flex items-center justify-center rounded-2xl px-6 py-3 text-sm font-medium transition-all duration-200;
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply disabled:pointer-events-none disabled:opacity-50;
  }

  /* Modern Card Component */
  .card-modern {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply rounded-2xl border bg-card text-card-foreground;
    box-shadow: var(--shadow-soft);
  }

  /* Container System */
  .container-modern {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Spacing System */
  .spacing-mobile {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply space-y-4;
  }

  .spacing-desktop {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply space-y-6 lg:space-y-8;
  }

  /* Shadow System */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  /* Responsive Grid System */
  .grid-responsive {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply grid gap-4 sm:gap-6 lg:gap-8;
    /* stylelint-disable-next-line at-rule-no-unknown */
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  /* Ultra-wide Support */
  @media (min-width: 120rem) {
    .grid-responsive {
      /* stylelint-disable-next-line at-rule-no-unknown */
      @apply grid-cols-5;
    }

    .container-modern {
      /* stylelint-disable-next-line at-rule-no-unknown */
      @apply px-12;
    }
  }

  @media (min-width: 160rem) {
    .grid-responsive {
      /* stylelint-disable-next-line at-rule-no-unknown */
      @apply grid-cols-6;
    }

    .container-modern {
      /* stylelint-disable-next-line at-rule-no-unknown */
      @apply px-16;
    }
  }
}